use std::collections::HashMap;

use once_cell::sync::Lazy;
use regex::Regex;

use crate::utils::{
    normalize_date,
    normalize_dns_question_name,
    normalize_time,
};

use crate::utils_classes::{
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    MYSQLValue,
    RouterConfig,
    RouterBoardConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    UserAuditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
};

use crate::utils_patterns::{
    DAEMON_PATTERN,
    DHCP_PATTERN,
    DHCP_REST_PATTERN,
    DNS_PATTERN,
    DNS_REST_PATTERN,
    FILTERLOG_PATTERN,
    ROUTER_PATTERN,
    ROUTERBOARD_PATTERN,
    SNORT_PATTERN,
    SQUID_PATTERN,
    SWITCH_PATTERN,
    USERAUDIT_PATTERN,
    USERNOTICE_PATTERN,
    USERWARNING_PATTERN,
    VMWARE_PATTERN,
    VPNSERVER_PATTERN,
    WINDOWSSERVER_PATTERN,
    WS_AN_AD_PATTERN,
    WS_SW_PATTERN,
};

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum ConfigType {
    Daemon,
    DHCP,
    DNS,
    FilterLog,
    Router,
    RouterBoard,
    Snort,
    Squid,
    Switch,
    UserAudit,
    UserNotice,
    UserWarning,
    VMware,
    VPNServer,
    WindowsServer,
}

impl ConfigType {
    #[inline(always)]
    pub fn get_slug(&self) -> &'static str {
        match self {
            ConfigType::Daemon        => "daemon",
            ConfigType::DHCP          => "dhcp",
            ConfigType::DNS           => "dns",
            ConfigType::FilterLog     => "filterlog",
            ConfigType::Router        => "router",
            ConfigType::RouterBoard   => "routerboard",
            ConfigType::Snort         => "snort",
            ConfigType::Squid         => "squid",
            ConfigType::Switch        => "switch",
            ConfigType::UserAudit     => "useraudit",
            ConfigType::UserNotice    => "usernotice",
            ConfigType::UserWarning   => "userwarning",
            ConfigType::VMware        => "vmware",
            ConfigType::VPNServer     => "vpnserver",
            ConfigType::WindowsServer => "windowsserver",
        }
    }
}

#[inline(always)]
pub fn _is_invalid_ln(ln: &str) -> bool {
    // __HAS_TEST__
    ln.is_empty()
        || ln.contains("ERROR name exceeds safe print buffer length")
        || ln.contains("ERROR length byte")
        || ln.contains("leads outside message")
        || ln.contains("Exiting on signal")
        || ln.contains("Now monitoring attacks")
        || ln.contains("spp_arpspoof")
        || ln.contains("because it is a directory, not a file")
        || !ln.chars().next().map_or(false, |c| c.is_ascii_digit())
}

#[inline(always)]
fn _invalid_line_sections(
    object_list_of_names_and_addresses: &[String],
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
    line_object: &str,
    line_event_type: &str,
    line_alert_type: &str,
) -> bool {
    // early return if none of them exists
    if event_types.is_empty() && filterby.is_empty() && line_object.is_empty() {
        return false;
    }

    // added 'if line_object' because
    // DHCP, DNS and VPN Server have no line_object
    if !line_object.is_empty() {
        // Use binary search if the list is sorted, otherwise use contains for small lists
        if object_list_of_names_and_addresses.len() > 10 {
            if !object_list_of_names_and_addresses.iter().any(|s| s.as_str() == line_object) {
                return true;
            }
        } else {
            if !object_list_of_names_and_addresses.iter().any(|s| s.as_str() == line_object) {
                return true;
            }
        }
    }

    if !event_types.is_empty() {
        if !event_types.iter().any(|s| s.as_str() == line_event_type) {
            return true;
        }
    }

    if filterby_is_in_alert_type && !line_alert_type.contains(filterby) {
        return true;
    }

    false
}

#[inline(always)]
fn _extract_matches_from_pattern<'a>(
    string: &'a str,
    pattern: &Regex,
) -> Option<Vec<&'a str>> {
    let caps = pattern.captures(string)?;
    let mut result = Vec::with_capacity(caps.len().saturating_sub(1));
    for i in 1..caps.len() {
        result.push(caps.get(i).map_or("", |m| m.as_str()));
    }
    Some(result)
}

fn _parse_daemon(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &DAEMON_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (daemon/err), ...
    let line_alert_type = splited[4];  // [radiusd]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> Sensor-1 (JUMP_1)
    let line_sensor_resolved = object_dict_of_addresses_and_names
        .get(line_sensor)
        .map(String::as_str)
        .unwrap_or(line_sensor);

    let row = vec![
        splited[0].to_string(),  // 2023-05-12
        splited[1].to_string(),  // 23:36:10
        line_event_type.to_string(),
        line_alert_type.to_string(),
        splited[5].to_string(),  // message
    ];

    (Some(line_sensor_resolved.to_string()), Some(row))
}

fn _parse_dhcp(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    if ln.contains("ID,Date,Time,Description,IP Address,Host Name,MAC Address,User Name") {
        return (None, None);
    }

    let splited = match _extract_matches_from_pattern(ln, &DHCP_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    // let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (syslog/info)
    let line_alert_type = splited[4];  // [dhcp]
    let rest            = splited[5];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        "",  // line_object
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let rest_splited = match _extract_matches_from_pattern(rest, &DHCP_REST_PATTERN) {
        Some(r_s) => r_s,
        None => return (None, None),
    };

    let mut row = Vec::with_capacity(19);
    row.push(normalize_date(&rest_splited[1]));  // 05/12/23 -> 2023-05-12
    for i in [2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18] {
        row.push(rest_splited[i].to_string());
    }

    (None, Some(row))
}

fn _parse_dns(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &DNS_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    // let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (syslog/info)
    let line_alert_type = splited[4];  // [dns]
    let rest            = splited[5];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        "",  // line_object
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // rest may be:
    //
    // 5/26/2023 8:40:00 PM 1510 PACKET 00000162B0 UDP Rcv *********** 0007   Q [0000      NOERROR]  A    (3)www(62)gooooooogle(3)com(0)
    // 5/26/2023 8:40:00 PM 1510 PACKET 00000162B3 UDP Rcv *********** 0005   Q [0000      NOERROR]  PTR  (3)216(2)58(3)202(1)4(7)in-addr(4)arpa(0)
    // 5/26/2023 12:13:45 PM 1018 PACKET 00000162B UDP Snd *******     6153   Q [0001   D  NOERROR]  AAAA (2)ds(9)kaspersky(3)com(0)
    // 5/26/2023 12:13:47 PM 1904 PACKET 00000162B UDP Rcv *********** c312   Q [0001   D  NOERROR]  A    (3)dc1(3)ksn(14)kaspersky-labs(3)com(0)
    // 5/26/2023 12:13:42 PM 1018 PACKET 00000162B UDP Snd *********** 8916 R Q [8281   DR SERVFAIL] AAAA (17)connectivitycheck(7)gstatic(3)com(0)
    // 5/26/2023 12:13:42 PM 1018 PACKET 00000162B UDP Snd *********** bb63 R Q [8281   DR SERVFAIL] A    (17)connectivitycheck(7)gstatic(3)com(0)
    // 5/26/2023 12:13:28 PM 1B00 PACKET 00000162B UDP Snd *********** cccf R Q [8385 A DR NXDOMAIN] A    (5)graph(8)facebook(3)com(3)abc(5)local(0)
    // 5/26/2023 12:13:41 PM 1510 PACKET 00000162A UDP Snd *********** 275c R Q [8385 A DR NXDOMAIN] AAAA (1)0(7)example(4)pool(3)ntp(3)org(3)abc(5)local(0)

    let rest_splited = match _extract_matches_from_pattern(rest, &DNS_REST_PATTERN) {
        Some(r_s) => r_s,
        None => return (None, None),
    };

    let mut row = Vec::with_capacity(16);
    row.push(normalize_date(&rest_splited[0]));  // 05/12/23 -> 2023-05-12

    // Optimize time formatting by avoiding format! macro
    let mut time_str = String::with_capacity(rest_splited[1].len() + rest_splited[2].len() + 1);
    time_str.push_str(rest_splited[1]);
    time_str.push(' ');
    time_str.push_str(rest_splited[2]);
    row.push(normalize_time(&time_str));  // 8:17:46 PM -> 20:17:46

    // Add remaining fields efficiently
    for i in 3..=15 {
        row.push(rest_splited[i].to_string());
    }

    // Handle the last field with special processing
    row.push(normalize_dns_question_name(&rest_splited[16]).to_lowercase());

    (None, Some(row))
}

fn _parse_filterlog(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &FILTERLOG_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (local0/info)
    let line_alert_type = splited[4];  // [filterlog]
    let rest            = splited[5];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // Use iterator directly instead of collecting into Vec first
    let mut rest_splited = rest.split(',');

    // rule_number = String::new();
    // sub_rule_number = String::new();
    // anchor = String::new();
    let mut tracking_id = String::new();
    let mut real_interface = String::new();
    let mut reason = String::new();
    let mut action = String::new();
    let mut direction = String::new();
    let mut ip_version = String::new();
    // tos = String::new();
    // ecn = String::new();
    // ttl = String::new();
    // id_ = String::new();
    // offset = String::new();
    // flags = String::new();
    // class_ = String::new();
    // flow_label = String::new();
    // hop_limit = String::new();
    // protocol_id = String::new();
    let mut protocol_name = String::new();
    // length = String::new();
    let mut source_ip = String::new();
    let mut destination_ip = String::new();
    let mut source_port = String::new();
    let mut destination_port = String::new();
    // data_length = String::new();
    // tcp_flags = String::new();
    // sequence_number = String::new();
    // ack = String::new();
    // window = String::new();
    // urg = String::new();
    // options = String::new();
    // type_ = String::new();
    // ttl_or_hop_limit = String::new();
    // vhid = String::new();
    // version = String::new();
    // advskew = String::new();
    // advbase = String::new();

    // Skip first 3 fields and get the next 6 fields we need
    for _ in 0..3 { rest_splited.next(); }

    if let (Some(tracking_id_str), Some(real_interface_str), Some(reason_str),
            Some(action_str), Some(direction_str), Some(ip_version_str)) =
        (rest_splited.next(), rest_splited.next(), rest_splited.next(),
         rest_splited.next(), rest_splited.next(), rest_splited.next()) {

        tracking_id    = tracking_id_str.to_string();
        real_interface = real_interface_str.to_string();
        reason         = reason_str.to_string();
        action         = action_str.to_string();
        direction      = direction_str.to_string();
        ip_version     = ip_version_str.to_string();

        match ip_version_str {
            "4" => {
                // Skip 7 fields: tos, ecn, ttl, id_, offset, flags, protocol_id
                for _ in 0..7 { rest_splited.next(); }

                if let Some(protocol_name_str) = rest_splited.next() {
                    protocol_name = protocol_name_str.to_lowercase();
                } else {
                    return (None, None);
                }
            },
            "6" => {
                // Skip 3 fields: class_, flow_label, hop_limit
                for _ in 0..3 { rest_splited.next(); }

                if let Some(protocol_id_str) = rest_splited.next() {
                    protocol_name = protocol_id_str.to_lowercase();
                    rest_splited.next(); // skip protocol_name_str
                } else {
                    return (None, None);
                }
            },
            _ => return (None, None),
        }

        rest_splited.next();  // length

        rest_splited.next();  // skip length

        // Get source and destination IPs
        if let (Some(source_ip_str), Some(destination_ip_str)) = (rest_splited.next(), rest_splited.next()) {
            source_ip = source_ip_str.to_string();
            destination_ip = destination_ip_str.to_string();
        } else {
            return (None, None);
        }

        match protocol_name.as_str() {
            "udp" => {
                if let (Some(source_port_str), Some(destination_port_str)) = (rest_splited.next(), rest_splited.next()) {
                    source_port = source_port_str.to_string();
                    destination_port = destination_port_str.to_string();
                    rest_splited.next();  // data_length
                }
            },
            "tcp" => {
                if let (Some(source_port_str), Some(destination_port_str)) = (rest_splited.next(), rest_splited.next()) {
                    source_port = source_port_str.to_string();
                    destination_port = destination_port_str.to_string();
                    // Skip 6 fields: data_length, tcp_flags, sequence_number, ack, window, urg, options
                    for _ in 0..7 { rest_splited.next(); }
                }
            },
            _ => {}
        }
        // else if protocol_name == "tcp" {
        //     rest_splited.next();  // type_
        //     rest_splited.next();  // ttl_or_hop_limit
        //     rest_splited.next();  // vhid
        //     rest_splited.next();  // version
        //     rest_splited.next();  // advskew
        //     rest_splited.next();  // advbase
        // }
    } else {
        return (None, None);
    }

    // *********** -> Sensor-1 (JUMP_1)
    let line_sensor_resolved = object_dict_of_addresses_and_names
        .get(line_sensor)
        .map(String::as_str)
        .unwrap_or(line_sensor);

    let row = vec![
        splited[0].to_string(),  // 2023-05-12
        splited[1].to_string(),  // 23:36:10
        protocol_name,
        source_ip,
        destination_ip,
        source_port,
        destination_port,
        tracking_id,
        real_interface,
        reason,
        action,
        direction,
    ];

    (Some(line_sensor_resolved.to_string()), Some(row))
}

fn _parse_router(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &ROUTER_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_router     = splited[2];  // Router1 OR ***********
    let line_event_type = splited[3];  // (local7/err), ...
    let line_alert_type = splited[4];  // [php-fpm]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_router,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> Router-1 (JUMP_1)
    let line_router_resolved = object_dict_of_addresses_and_names
        .get(line_router)
        .map(String::as_str)
        .unwrap_or(line_router);

    let row = vec![
        splited[0].to_string(),  // 2023-05-12
        splited[1].to_string(),  // 23:36:10
        line_event_type.to_string(),
        line_alert_type.to_string(),
        splited[5].to_string(),  // message
    ];

    (Some(line_router_resolved.to_string()), Some(row))
}

fn _parse_routerboard(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &ROUTERBOARD_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_routerboard = splited[2];  // RouterBoard1 OR ***********
    let line_event_type  = splited[3];  // (local7/err), ...
    let line_alert_type  = splited[4];  // [php-fpm]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_routerboard,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> RouterBoard-1 (JUMP_1)
    let line_routerboard_resolved = object_dict_of_addresses_and_names
        .get(line_routerboard)
        .map(String::as_str)
        .unwrap_or(line_routerboard);

    let row = vec![
        splited[0].to_string(),  // 2023-05-12
        splited[1].to_string(),  // 23:36:10
        line_event_type.to_string(),
        line_alert_type.to_string(),
        splited[5].to_string(),  // message
    ];

    (Some(line_routerboard_resolved.to_string()), Some(row))
}

fn _parse_snort(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &SNORT_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (daemon/err), ...
    let line_alert_type = splited[4];  // [snort]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> Sensor-1 (JUMP_1)
    let line_sensor_resolved = object_dict_of_addresses_and_names
        .get(line_sensor)
        .map(String::as_str)
        .unwrap_or(line_sensor);

    let mut row = Vec::with_capacity(11);
    row.push(splited[0].to_string());   // 2023-05-12
    row.push(splited[1].to_string());   // 23:36:10
    for i in 5..=13 {
        row.push(splited[i].to_string());
    }

    (Some(line_sensor_resolved.to_string()), Some(row))
}

fn _parse_squid(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &SQUID_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (daemon/err), ...
    let line_alert_type = splited[4];  // [(squid-1)]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> Sensor-1 (JUMP_1)
    let line_sensor_resolved = object_dict_of_addresses_and_names
        .get(line_sensor)
        .map(String::as_str)
        .unwrap_or(line_sensor);

    let mut row = Vec::with_capacity(12);
    row.push(splited[0].to_string());                  // 2023-05-12
    row.push(splited[1].to_string());                  // 23:36:10
    // Skip splited[5] (timestamp)
    for i in 6..=11 {
        row.push(splited[i].to_string());
    }
    row.push(splited[12].to_lowercase());              // url (lowercase)
    for i in 13..=16 {
        row.push(splited[i].to_string());
    }

    (Some(line_sensor_resolved.to_string()), Some(row))
}

fn _parse_switch(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &SWITCH_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_switch     = splited[2];  // Switch1 OR ***********
    let line_event_type = splited[3];  // (local7/err), ...
    let line_alert_type = splited[4];  // [php-fpm]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_switch,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> Switch-1 (JUMP_1)
    let line_switch_resolved = object_dict_of_addresses_and_names
        .get(line_switch)
        .map(String::as_str)
        .unwrap_or(line_switch);

    let row = vec![
        splited[0].to_string(),  // 2023-05-12
        splited[1].to_string(),  // 23:36:10
        line_event_type.to_string(),
        line_alert_type.to_string(),
        splited[5].to_string(),  // message
    ];

    (Some(line_switch_resolved.to_string()), Some(row))
}

fn _parse_useraudit(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    if !ln.contains("Successful login") && !ln.contains("User logged out") {
        return (None, None);
    }

    let splited = match _extract_matches_from_pattern(ln, &USERAUDIT_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (daemon/err), ...
    let line_alert_type = splited[4];  // [php-fpm]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> Sensor-1 (JUMP_1)
    let line_sensor_resolved = object_dict_of_addresses_and_names
        .get(line_sensor)
        .map(String::as_str)
        .unwrap_or(line_sensor);

    let row = vec![
        splited[0].to_string(),     // 2023-05-12
        splited[1].to_string(),     // 23:36:10
        line_alert_type.to_string(),
        splited[5].to_string(),     // message
    ];

    (Some(line_sensor_resolved.to_string()), Some(row))
}

fn _parse_usernotice(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &USERNOTICE_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (user/notice)
    let line_alert_type = splited[4];  // [openvpn]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> Sensor-1 (JUMP_1)
    let line_sensor_resolved = object_dict_of_addresses_and_names
        .get(line_sensor)
        .map(String::as_str)
        .unwrap_or(line_sensor);

    let mut row = Vec::with_capacity(7);
    row.push(splited[0].to_string());  // 2023-05-12
    row.push(splited[1].to_string());  // 23:36:10
    for i in 5..=9 {
        row.push(splited[i].to_string());
    }

    (Some(line_sensor_resolved.to_string()), Some(row))
}

fn _parse_userwarning(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &USERWARNING_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (user/warning)
    let line_alert_type = splited[4];  // [radiusd]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> Sensor-1 (JUMP_1)
    let line_sensor_resolved = object_dict_of_addresses_and_names
        .get(line_sensor)
        .map(String::as_str)
        .unwrap_or(line_sensor);

    let row = vec![
        splited[0].to_string(),  // 2023-05-12
        splited[1].to_string(),  // 23:36:10
        line_alert_type.to_string(),
        splited[5].to_string(),  // message
    ];

    (Some(line_sensor_resolved.to_string()), Some(row))
}

fn _parse_vmware(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &VMWARE_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_vmware     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (user/warning)
    let line_alert_type = splited[4];  // [radiusd]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_vmware,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // *********** -> VMware-1 (JUMP_1)
    let line_vmware_resolved = object_dict_of_addresses_and_names
        .get(line_vmware)
        .map(String::as_str)
        .unwrap_or(line_vmware);

    let row = vec![
        splited[0].to_string(),  // 2023-05-12
        splited[1].to_string(),  // 23:36:10
        line_event_type.to_string(),
        line_alert_type.to_string(),
        splited[5].to_string(),  // message
    ];

    (Some(line_vmware_resolved.to_string()), Some(row))
}

fn _parse_vpnserver(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &VPNSERVER_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    // let line_sensor     = splited[2];  // Sensor-1 OR ***********
    let line_event_type = splited[3];  // (user/info)
    let line_alert_type = splited[4];  // [MSWinEventLog        1       System  5296793 Mon]
    // let rest            = splited[5];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        "",  // line_object
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let mut row = Vec::with_capacity(8);
    row.push(splited[0].to_string());   // 2023-05-12
    row.push(splited[1].to_string());   // 23:36:10
    for i in 5..=10 {
        row.push(splited[i].to_string());
    }

    (None, Some(row))
}

fn _parse_windowsserver(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    if ln.contains("[dns]") || ln.contains("[dhcp]") {
        return (None, None);
    }

    let splited = match _extract_matches_from_pattern(ln, &WINDOWSSERVER_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_windowsserver = splited[2];  // WindowsServer1 OR ***********
    let line_event_type    = splited[3];  // (daemon/err), ...
    let line_alert_type    = splited[4];  // [MSWinEventLog 1 N/A 2873106 Fri]
    let message            = splited[5].replace("\t", " ");

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_windowsserver,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }



    // START __prepare_more_variables__

    // event_id - optimize by avoiding Vec allocation
    let event_id = message.split_whitespace()
        .nth(4)
        .map(|s| s.to_string())
        .unwrap_or_else(|| String::new());

    // category
    let category = WindowsServerConfig::get_category_from_event_id(&event_id);

    // potential_criticality
    let potential_criticality = WindowsServerConfig::EVENT_IDS_AND_POTENTIAL_CRITICALITIES.value().value_dict()
                                .get(&event_id)
                                .cloned()
                                .unwrap_or_else(|| "".to_string());  // High, Medium, ...

    // account_name, account_domain
    let (account_name, account_domain) = if let Some(account_matches) = _extract_matches_from_pattern(&message, &WS_AN_AD_PATTERN) {
        (account_matches[0].to_string(), account_matches[1].to_string())  // ('SYSTEM', 'NT AUTHORITY')
    } else {
        ("".to_string(), "".to_string())
    };

    // source workstation
    let source_workstation = if let Some(sw_matches) = _extract_matches_from_pattern(&message, &WS_SW_PATTERN) {
        sw_matches[0].to_string()  // Windows7
    } else {
        "".to_string()
    };

    // END __prepare_more_variables__


    // *********** -> WindowsServer-1 (JUMP_1)
    let line_windowsserver_resolved = object_dict_of_addresses_and_names
        .get(line_windowsserver)
        .map(String::as_str)
        .unwrap_or(line_windowsserver);

    let row = vec![
        splited[0].to_string(),  // 2023-05-12
        splited[1].to_string(),  // 23:36:10
        line_alert_type.to_string(),
        message,
        event_id,
        category,
        potential_criticality,
        account_name,
        account_domain,
        source_workstation,
    ];

    (Some(line_windowsserver_resolved.to_string()), Some(row))
}

static _PRECOMPUTED_VALUES: Lazy<HashMap<&'static str, (String, String, bool, Vec<String>)>> = Lazy::new(|| {
    // Pre-allocate with capacity for all known config types
    let mut map = HashMap::with_capacity(15);

    macro_rules! add_config {
        ($typ:ident, $slug:expr) => {{
            let slug = $typ::SLUG.value().value_string();
            let filterby = $typ::FILTERBY.value().value_string();
            let filterby_is_in_alert_type = $typ::FILTERBY_IS_IN_ALERT_TYPE.value().value_bool();
            let event_types = $typ::EVENT_TYPES.value().value_list();
            map.insert($slug, (slug, filterby, filterby_is_in_alert_type, event_types));
        }};
    }

    add_config!(DaemonConfig,        "daemon");
    add_config!(DHCPConfig,          "dhcp");
    add_config!(DNSConfig,           "dns");
    add_config!(FilterLogConfig,     "filterlog");
    add_config!(RouterConfig,        "router");
    add_config!(RouterBoardConfig,   "routerboard");
    add_config!(SnortConfig,         "snort");
    add_config!(SquidConfig,         "squid");
    add_config!(SwitchConfig,        "switch");
    add_config!(UserAuditConfig,     "useraudit");
    add_config!(UserNoticeConfig,    "usernotice");
    add_config!(UserWarningConfig,   "userwarning");
    add_config!(VMwareConfig,        "vmware");
    add_config!(VPNServerConfig,     "vpnserver");
    add_config!(WindowsServerConfig, "windowsserver");

    map
});

static _PARSERS: Lazy<HashMap<&'static str, fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>)>> = Lazy::new(|| {
    // Pre-allocate with capacity for all known parsers
    let mut map = HashMap::with_capacity(15);
    map.insert("daemon",        _parse_daemon        as _);
    map.insert("dhcp",          _parse_dhcp          as _);
    map.insert("dns",           _parse_dns           as _);
    map.insert("filterlog",     _parse_filterlog     as _);
    map.insert("router",        _parse_router        as _);
    map.insert("routerboard",   _parse_routerboard   as _);
    map.insert("snort",         _parse_snort         as _);
    map.insert("squid",         _parse_squid         as _);
    map.insert("switch",        _parse_switch        as _);
    map.insert("useraudit",     _parse_useraudit     as _);
    map.insert("usernotice",    _parse_usernotice    as _);
    map.insert("userwarning",   _parse_userwarning   as _);
    map.insert("vmware",        _parse_vmware        as _);
    map.insert("vpnserver",     _parse_vpnserver     as _);
    map.insert("windowsserver", _parse_windowsserver as _);
    map
});

#[inline(always)]
pub fn parse_ln(
    ln: &str,
    cls: ConfigType,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
) -> (Option<String>, Option<Vec<String>>) {
    // __HAS_TEST__

    if _is_invalid_ln(ln) {
        return (None, None);
    }

    let slug = cls.get_slug();
    let (_, filterby, filterby_is_in_alert_type, event_types) =
        match _PRECOMPUTED_VALUES.get(slug) {
            Some(v) => v,
            None => return (None, None),
        };

    // Early filterby check for performance
    if !filterby.is_empty() && !*filterby_is_in_alert_type && !ln.contains(filterby) {
        return (None, None);
    }

    // Direct function lookup using slug
    let func = match _PARSERS.get(slug) {
        Some(f) => f,
        None => return (None, None),
    };

    func(
        ln,
        object_list_of_names_and_addresses,
        object_dict_of_addresses_and_names,
        event_types,
        *filterby_is_in_alert_type,
        filterby,
    )
}
